import sys
import traceback
from slack import send_error_message
from state import state
def global_exception_handler(exc_type, exc_value, exc_traceback):
    # Log the error
    print(f"FATAL ERROR: {exc_type.__name__}: {exc_value}")
    print("Traceback:")
    traceback.print_tb(exc_traceback)
    send_error_message(
        app_name=state.get("TARGET_APP"),
        environment=state.get("TARGET_VPS"),
        message="An error occurred during the version sync process.",
        details=f"{exc_type.__name__}: {exc_value}"
    )
    # Add your failure handling code here
    # For example, send notification, clean up resources, etc.
    
    # Exit with error code
    sys.exit(1)
