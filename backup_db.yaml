- name: Backup the database of the target app
  hosts: localhost  # Start on localhost to determine the target VPS
  gather_facts: false
  remote_user: ubuntu
  vars_files:
    - vars/apps_to_vps.yaml
    - vars/apps_to_db_name.yaml

  vars:
      target_password: "{{ lookup('env', target_vps | upper + '_SSH_PASSWORD') }}"
      target_vps: "{{ app_to_vps[target_app] }}"
      db_name : "{{ app_to_db_name[target_app] }}"

  tasks:
    - name: Verify if the target app is valid
      fail:
        msg: "The target app '{{ target_app }}' is not valid. Allowed apps: {{ app_to_vps.keys() | list }}"
      when: target_app not in app_to_vps

    - name: Verify if the the app has a database
      fail:
        msg: "The target app '{{ target_app }}' is not valid. Allowed apps: {{ app_to_db_name.keys() | list }}"
      when: target_app not in app_to_db_name
    #  run the backup script on target vps

    - name: Set the script path for the target VPS
      set_fact:
        script_path: "{{ backup_script_paths[target_app] }}"
    
    - name: Execute the backup script on the target VPS
      command: >
        "{{ script_path }}" "{{ db_name }}"
      delegate_to: "{{ target_vps  }}"
      vars:
        ansible_ssh_pass: "{{ target_password }}"
      register: backup_result

    - name: Display script output
      debug:
        var: backup_result.stdout_lines
        

    
  
