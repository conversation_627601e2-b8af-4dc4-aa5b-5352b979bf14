import requests
import re
from urllib.parse import urlencode
from state import state
import os
from requests.auth import HTTPBasicAuth
from xml.etree import ElementTree as ET
from logger import log

def get_versions_by_environment():
    """
    Get application versions by environment from a Nexus repository.
    
    Args:
        config (dict): A dictionary containing:
            - url: Nexus repository base URL
            - repo: Repository name
            - appname: Application name
            - username: Username for authentication
            - password: Password for authentication
            
    Returns:
        dict: A dictionary mapping environment names to lists of versions
    """
    # 1. Validate config
    log.info(f"Getting version information for {state.get('TARGET_APP')} from Nexus repository")
    config = {
        "url": state.get("NEXUS_URL"),
        "repo": state.get("NEXUS_REPO"),
        "appname": state.get("NEXUS_APPNAME"),
        "username": state.get("NEXUS_USERNAME"),
        "password": state.get("NEXUS_PASSWORD")
    }
    
    # 2. Prepare
    versions_by_env = {}
    base_url = f"{config['url']}/service/rest/v1/assets"
    url = base_url
    params = {"repository": config["repo"]}
    
    # Regex to match paths like "some/path/envName/appname-1.0.0.zip"
    version_regex = re.compile(f"(.+)/{config['appname']}-(.+)\\.zip")
    
    # 3. Loop through pages until no continuationToken is returned
    while url:
        # Build URL with query params
        query_string = urlencode(params)
        full_url = f"{url}?{query_string}" if query_string else url
        
        # 4. Make request with Basic Auth
        try:
            auth = (config["username"], config["password"])
            response = requests.get(full_url, auth=auth, headers={"Accept": "application/json"})
            
            if not response.ok:
                final_msg = f"Request failed with status: {response.status_code} {response.text}"
                if response.status_code == 401:
                    final_msg += " Authentication failed. Please check your Nexus credentials."
                elif response.status_code == 403:
                    final_msg += " Access denied. You do not have permission to access this resource."
                elif response.status_code == 404:
                    final_msg += " Resource not found. Please check the repository URL and name."
                elif response.status_code == 500:
                    final_msg += " Internal server error. Please check the Nexus server logs for more details."
                # bad gateway or service unavailable, retry after a short delay
                elif response.status_code in [502, 503]:
                    final_msg += " Nexus service is temporarily unavailable. Verifying the service is up"
                
                log.error(final_msg)
                raise requests.HTTPError(final_msg)
                
            data = response.json()
        except Exception as error:
            log.error(f"Request failed: {str(error)}")
            break
        
        if not data or "items" not in data:
            break
        
        # 5. Process each asset
        for asset in data["items"]:
            path = asset.get("path", "")
            match = version_regex.search(path)
            if match:
                full_env_path = match.group(1)
                version = match.group(2)
                # The environment is assumed to be the last directory in the path
                env = full_env_path.split('/')[-1]
                if env not in versions_by_env:
                    versions_by_env[env] = []
                versions_by_env[env].append(version)
        
        # 6. Handle pagination
        continuation_token = data.get("continuationToken")
        if continuation_token:
            url = base_url
            params = {
                "repository": config["repo"],
                "continuationToken": continuation_token
            }
        else:
            url = None
    
    # 7. Sort and deduplicate versions for each environment
    for env in versions_by_env:
        unique_versions = set(versions_by_env[env])
        # Sort by splitting version numbers into components and converting to integers
        sorted_versions = sorted(unique_versions, 
                               key=lambda v: [int(x) for x in v.split('.')],reverse=True)
        versions_by_env[env] = sorted_versions
    
    return versions_by_env


def get_jenkins_config():
    log.info(f"Getting Jenkins config.xml for {state.get('TARGET_APP')} from {state.get('JENKINS_URL')}")
    url = f"{state.get('JENKINS_URL')}/config.xml"
    response = requests.get(url, auth=HTTPBasicAuth(state.get("JENKINS_USERNAME"), state.get("JENKINS_TOKEN")))

    if response.status_code == 200:
        # save config.xml to file
        with open(f"tmp/{state.get('TARGET_APP')}_config.xml", "w") as f:
            f.write(response.text)
        log.info(f"{state.get('TARGET_APP')}_config.xml saved to file successfully.")
    else:
        raise Exception(f"Failed to get Jenkins config.xml: {response.status_code} {response.text}")
    
def update_version_choices(versions):
    """
    Update the VERSION parameter choices in the config.xml file.
    
    :param versions: List of version strings to update in the config.xml.
    """
    if not os.path.exists(f"tmp/{state.get('TARGET_APP')}_config.xml"):
        raise FileNotFoundError(f" {state.get('TARGET_APP')}_config.xml not found. Please ensure it exists.")

    # Parse the XML file
    tree = ET.parse(f"tmp/{state.get('TARGET_APP')}_config.xml")
    root = tree.getroot()

    # Find the VERSION parameter
    parameter_definitions = root.find(".//parameterDefinitions")
    if parameter_definitions is None:
        raise Exception(f"parameterDefinitions not found in {state.get('TARGET_APP')}_config.xml")
    
    version_param_found = False
    for param in parameter_definitions:
        if param.find("name").text == "VERSION":
            version_param_found = True
            # Update the choices
            choices = param.find("choices")
            if choices is not None:
                string_array = choices.find("a")
                if string_array is not None:
                    # Add the class attribute if it doesn't exist
                    if "class" not in string_array.attrib:
                        string_array.set("class", "string-array")
                    # Clear existing choices
                    string_array.clear()
                    # Add new choices
                    for version in versions:
                        ET.SubElement(string_array, "string").text = version
                    break
                else:
                    raise Exception("Could not find string array element in VERSION parameter choices")
            else:
                raise Exception("Could not find choices element in VERSION parameter")
    
    if not version_param_found:
        raise Exception("VERSION parameter not found in the configuration")

    # Save the updated XML back to the file
    tree.write(f"tmp/{state.get('TARGET_APP')}_config.xml", encoding="UTF-8", xml_declaration=True)
    log.info("VERSION parameter choices updated successfully.")


def post_jenkins_config():
    url = f"{state.get('JENKINS_URL')}/config.xml"
    with open(f"tmp/{state.get('TARGET_APP')}_config.xml", "r") as f:
        data = f.read()
    response = requests.post(url, data=data, auth=HTTPBasicAuth(state.get("JENKINS_USERNAME"), state.get("JENKINS_TOKEN")), headers={"Content-Type": "application/xml"})
    if response.status_code == 200:
        log.info(f"tmp/{state.get('TARGET_APP')}_config.xml posted to Jenkins successfully.")
    else:
        log.error(f"Failed to post tmp/{state.get('TARGET_APP')}_config.xml to Jenkins: {response.status_code} {response.text}")
        raise Exception(f"Failed to post tmp/{state.get('TARGET_APP')}_config.xml to Jenkins: {response.status_code} {response.text}")