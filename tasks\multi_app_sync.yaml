---
# Multi app mode tasks for selected prod apps
- name: Display selected prod apps that will be processed
  debug:
    msg: "Processing selected prod apps: {{ selected_prod_apps }}"

- name: Execute the Product Sync script for each selected prod app
  command: >
    "{{ vps_script_paths[item] }}"
  delegate_to: "{{ app_to_vps[item] }}"
  vars:
    ansible_ssh_pass: "{{ lookup('env', app_to_vps[item] | upper + '_SSH_PASSWORD') }}"
  register: maintenance_results
  loop: "{{ selected_prod_apps }}"
  loop_control:
    loop_var: item
  when: item in vps_script_paths
  
- name: Display results for each prod app
  debug:
    msg: 
      - "App: {{ item.item }}"
      - "Status: {{ 'Success' if item.rc == 0 else 'Failed' }}"
      - "Output: {{ item.stdout_lines | default(['No output']) }}"
  loop: "{{ maintenance_results.results | default([]) }}"
  loop_control:
    loop_var: item
  when: maintenance_results is defined

- name: Report status for each prod app to Slack
  uri:
    url: "{{ lookup('env', 'SLACK_WEBHOOK_URL') }}"
    method: POST
    headers:
      Content-Type: "application/json"
    body: |
      {
        "text": "{{ 'Product & Fournisseur and Transco Produit sync script ' + ('succeeded' if item.rc == 0 else 'failed') + ' for ' + item.item + ('' if item.rc == 0 else ': ' + item.stderr) }}"
      }
    body_format: json
  loop: "{{ maintenance_results.results | default([]) }}"
  loop_control:
    loop_var: item
  when: maintenance_results is defined and lookup('env', 'SLACK_WEBHOOK_URL') != ''

- name: Check for any failures in prod apps
  fail:
    msg: "One or more sync scripts failed. Check the output above for details."
  when: maintenance_results is defined and maintenance_results.results | selectattr('rc', 'ne', 0) | list | length > 0
