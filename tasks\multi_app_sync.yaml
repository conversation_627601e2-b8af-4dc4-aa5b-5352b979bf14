---
# Multi app sync tasks - called from main playbook
- name: Display selected prod apps that will be processed
  debug:
    msg: "Processing selected prod apps: {{ selected_prod_apps }}"

- name: Create dynamic inventory for multi-app sync
  add_host:
    name: "{{ app_to_vps[item] }}"
    groups: "dynamic_{{ app_to_vps[item] }}"
    app_name: "{{ item }}"
    script_path: "{{ vps_script_paths[item] }}"
    ansible_ssh_pass: "{{ lookup('env', app_to_vps[item] | upper + '_SSH_PASSWORD') }}"
    ansible_ssh_user: "{{ lookup('env', app_to_vps[item] | upper + '_SSH_USER') | default('ubuntu') }}"
    ansible_port: "{{ lookup('env', 'SSH_PORT') }}"
  loop: "{{ selected_prod_apps }}"
  loop_control:
    loop_var: item
  delegate_to: localhost

- name: Execute sync operations on target hosts
  block:
    - name: Run app-specific sync script
      command: "{{ hostvars[item]['script_path'] }}"
      register: sync_results
      delegate_to: "{{ item }}"
      loop: "{{ groups['dynamic_' + app_to_vps[current_app]] | default([]) }}"
      loop_control:
        loop_var: item
      vars:
        current_app: "{{ selected_prod_apps[0] }}"  # This needs to be handled differently
  rescue:
    - name: Handle sync errors
      debug:
        msg: "Error occurred during sync operation: {{ ansible_failed_result.msg }}"
      
- name: Display sync results
  debug:
    msg:
      - "App: {{ item.item }}"
      - "Host: {{ hostvars[item.item]['app_name'] }}"
      - "Script: {{ hostvars[item.item]['script_path'] }}"
      - "Result: {{ 'SUCCESS' if item.rc == 0 else 'FAILED' }}"
      - "Output: {{ item.stdout }}"
      - "Error: {{ item.stderr }}"
  loop: "{{ sync_results.results | default([]) }}"
  when: sync_results is defined