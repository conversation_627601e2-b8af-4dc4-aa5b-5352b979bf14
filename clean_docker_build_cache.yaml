# clean docker build cache
- name: Clean docker build cache
  hosts: 
    - vps5
    - vps6
    - vps7
    - vps8
    - vps9
    - vps10
    - vps11

  become: true
  remote_user: ubuntu
  gather_facts: false
  strategy: free


  vars:
    df_output: ""
    #  declare ansibe_ssh_pass based on the host from env like 'VPS5_SSH_PASSWORD'
    ansible_ssh_pass : "{{ lookup('env', inventory_hostname | upper + '_SSH_PASSWORD') }}"
  tasks:

    - name: Print target var (won’t fail if it’s missing)
      debug:
        msg: "you passed {{ target | default('<not set>') }} to the playbook"



    - name: Debug docker system Occupied space
      command: docker system df
      register: df_output
      ignore_errors: yes
    - name: Print df output
      debug:
        var: df_output.stdout_lines
    
    - name: Prune docker builder cache
      command: docker builder prune -f
      ignore_errors: yes