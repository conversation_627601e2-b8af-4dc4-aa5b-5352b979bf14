import sys
from const import ALLOWED_VERSION_SYNC_APPS, DEFUALT_JENKINS_USERNAME,DEFUALT_NEXUS_URL
from nexus import get_versions_by_environment,get_jenkins_config, post_jenkins_config, update_version_choices
from utils import get_nexus_config_by_match ,resolve_job_path, resolve_target_vps
import os
from state import state
from error_handler import global_exception_handler
from slack import send_success_message
from logger import log
sys.excepthook = global_exception_handler

def parse_args(args):
    # use  sys args and manualy split and create dict
    parsed_args = {}
    for arg in args:
        if '=' in arg:
            key, value = arg.split('=', 1)
            parsed_args[key] = value
        else:
            print(f"Invalid argument format: {arg}")

    return parsed_args


# Example usage


def load_args_and_validate():
    parsed_args = parse_args(sys.argv[1:])
    if "target" not in parsed_args:
        print("No target specified")
        exit(1)

    if parsed_args["target"] not in ALLOWED_VERSION_SYNC_APPS:
        print(f"Invalid target application specified: {parsed_args['target']}")
        print(f"Allowed Apps are: {', '.join(ALLOWED_VERSION_SYNC_APPS)}")
        exit(1)
    return parsed_args


def validate_state():
    required_keys = [
        "JENKINS_URL", "JENKINS_USERNAME", "JENKINS_TOKEN",
        "TARGET_APP", "TARGET_VPS", "NEXUS_URL", 
        "NEXUS_REPO", "NEXUS_APPNAME", "NEXUS_USERNAME", "NEXUS_PASSWORD"
    ]
    
    for key in required_keys:
        value = state.get(key)
        if not value:
            raise ValueError(f"State validation failed: {key} is empty or missing.")        
        if key == "JENKINS_URL" and not value.startswith("https"):
            raise ValueError(f"State validation failed: {key} must be a valid URL and https.")
        if key == "NEXUS_URL" and not value.startswith("http"):
            raise ValueError(f"State validation failed: {key} must be a valid URL.")
        # Add more specific validations as needed for other keys

def init() :
    parsed_args = load_args_and_validate()
    target = parsed_args["target"]
    target_vps = resolve_target_vps(target)
    jenkins_full_path = resolve_job_path(target)
    log.info(f"lokking for jenkins token in env var JENKINS_{target_vps.upper()}_TOKEN")
    jenkins_token = os.environ.get(f"JENKINS_{target_vps.upper()}_TOKEN")
    nexus_config = get_nexus_config_by_match(target)    
    if not os.path.exists("tmp"):
        os.makedirs("tmp")
    nexus_password = os.environ.get(f"nexus_{nexus_config['appname'].lower()}")
    slack_webhook = os.environ.get("slack_webhook")
    state.update("JENKINS_URL",jenkins_full_path )
    state.update("JENKINS_USERNAME", DEFUALT_JENKINS_USERNAME)
    state.update("JENKINS_TOKEN", jenkins_token )
    state.update("TARGET_APP", target)
    state.update("TARGET_VPS", target_vps)
    state.update("TARGET_ENV",str(target).split('.')[-1])
    state.update("NEXUS_URL", DEFUALT_NEXUS_URL)
    state.update("NEXUS_REPO", nexus_config["repo"])
    state.update("NEXUS_APPNAME", nexus_config["appname"])
    state.update("NEXUS_USERNAME", nexus_config["username"])
    state.update("NEXUS_PASSWORD", nexus_password)
    state.update("SLACK_WEBHOOK", slack_webhook)
    validate_state()


# log all state for debugging
def log_state():
    MASK_STRING = "********"
    state_normalized = dict(state.state)
    state_normalized["NEXUS_PASSWORD"] =  MASK_STRING
    state_normalized["JENKINS_TOKEN"] = MASK_STRING
    state_normalized["SLACK_WEBHOOK"] = MASK_STRING
    state_normalized["NEXUS_USERNAME"] = MASK_STRING
    state_normalized["JENKINS_USERNAME"] = MASK_STRING

    print("Current state:")
    for key, value in state_normalized.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    init()
    # log_state()
    log.info("Starting version retrieval...")
    versions = get_versions_by_environment()
    log.info(f"Versions by environment: {versions}")

    target_env = state.get("TARGET_ENV")
    if target_env not in versions:
        raise ValueError(f"Target environment {target_env} not found in versions pulled from Nexus.")
    target_env_versions = versions[target_env]
    log.info(f"Getting Jenkins config for {target_env} environment...")
    get_jenkins_config()
    log.info("updating Jenkins config...")
    update_version_choices(target_env_versions)
    log.info("Posting Jenkins config...")
    post_jenkins_config()
    log.info("Sending Alert to slack...")
    send_success_message(
        app_name=state.get("NEXUS_APPNAME"),
        environment=state.get("TARGET_ENV"),
        message=f"Version sync completed successfully (*{target_env_versions[0]}* is the latest).",
        details=f"Versions updated to the latest deployed on Nexus for {state.get('TARGET_ENV')} environment.",
    )



