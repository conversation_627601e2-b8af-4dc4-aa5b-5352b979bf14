- name: Activate / Deactivate Maintenance Mode
  hosts: localhost  # Start on localhost to determine the target VPS
  gather_facts: false
  remote_user: ubuntu
  vars_files:
    - vars/apps_to_vps.yaml
    - vars/maintenance.yaml

  vars:
      target_password: "{{ lookup('env', target_vps | upper + '_SSH_PASSWORD') }}"
      target_vps: "{{ app_to_vps[target_app] }}"

  tasks:
    - name: Verify if the target app is valid
      fail:
        msg: "The target app '{{ target_app }}' is not valid. Allowed apps: {{ app_to_vps.keys() | list }}"
      when: target_app not in app_to_vps

    - name: Set the script path for the target VPS
      set_fact:
        script_path: "{{ vps_script_paths[target_vps] }}"

    - name: Execute the maintenance script on the target VPS
      command: >
        "{{ script_path }}" "{{ target_app }}" "{{ is_revert | default('') }}"
      delegate_to: "{{ target_vps  }}"
      vars:
        ansible_ssh_pass: "{{ target_password }}"
      register: maintenance_result

    - name: Display script output
      debug:
        var: maintenance_result.stdout_lines

    - name: Check for errors
      fail:
        msg: "Maintenance script failed with error: {{ maintenance_result.stderr }}"
      when: maintenance_result.rc != 0
