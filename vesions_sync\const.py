
DEFUALT_JENKINS_USERNAME = "manager"
DEFUALT_NEXUS_URL = "https://vps5.sophatel.com:8081"


# Constants for allowed applications
ALLOWED_VERSION_SYNC_APPS = [
    "winpharm.preprod",
    "winpharm.recette",
    "webfix.dev",
    "lacentrale.dev",
    "lacentrale.recette",
    "pharmalien.dev",
    "pharmalien.preprod",
    "winref.dev",


    "winpharm.winproduits",
    # "webfix.prod",
    # "winchat.prod",
    # "offialpharma.prod",
    "pharmalien.prod",
    "winpharm.prod",
    # "lacentrale.prod",
    "wingroupe.prod",
    "winref.prod",
]

# Jenkins URLs for different VPS servers
JENKINS_URLS = {
    "vps5": "https://vps5.sophatel.com:8443",
    "vps6": "https://vps6.sophatel.com:8443",
    "vps7": "https://vps7.sophatel.com:8443",
    "vps8": "https://vps8.sophatel.com:8443",
    "vps9": "https://vps9.sophatel.com:8443",
    "vps10": "https://vps10.sophatel.com:8443",
}

# Job paths for applications in Jenkins
APPS_TO_JOB_PATH = {
    # Vps5
    "lacentrale.dev": "job/LaCentralPharma/job/lacentralFront",
    "lacentrale.recette": "job/LaCentralPharma/job/lacentralFront",
    "pharmalien.dev": "job/pharmaLien/job/pharmaLienFront",
    "pharmalien.preprod": "job/pharmaLien/job/pharmaLienFront",
    "webfix.dev": "job/webFixGroupe/job/webFixGroupeFrontEnd",
    "winpharm.preprod": "job/Winpharm/job/winpharmFrontend",
    "winpharm.recette": "job/Winpharm/job/winpharmFrontend",
    "winref.dev": "job/Winref/job/WinrefFrontEnd",
    # Vps6
    "winpharm.winproduits": "job/winpharm/job/winpharmFrontend",
    "winchat.prod": "job/winchat/job/winchat-frontend",
    "offialpharma.prod": "job/officialpharma/job/officialpharma-frontend",
    "winref.prod": "job/Winref/job/WinrefFrontEnd",
    # vps 7

    # vps 7
    "pharmalien.prod": "job/Pharmhub/job/PharmahubFrontEnd",
    # vps 8
    "winpharm.prod": "job/winpharm/job/WinpharmFrontend",

    # vps 9
    "webfix.prod": "job/webFixGroupe/job/webFixGroupeFrontEnd",
    "lacentrale.prod": "job/LaCentralPharma/job/lacentralFront",
    # vps 10
    "wingroupe.prod": "job/WinGroupe/job/WinGroupeFrontEnd",

}

# Mapping of applications to VPS servers
APPS_TO_VPS = {
    # dev + preprod
    "winpharm.preprod": "vps5",
    "winpharm.recette": "vps5",
    "webfix.dev": "vps5",
    "lacentrale.dev": "vps5",
    "lacentrale.recette": "vps5",
    "pharmalien.dev": "vps5",
    "pharmalien.preprod": "vps5",
    "winref.dev": "vps5",

    # # prod
    "winpharm.winproduits": "vps6",
    "winref.prod": "vps6",
    # "webfix.prod": "vps9",
    # "winchat.prod": "vps6",
    # "offialpharma.prod": "vps6",
    "pharmalien.prod": "vps7",
    "winpharm.prod": "vps8",
    # "lacentrale.prod": "vps9",
    "wingroupe.prod": "vps10",
}


#  nexus mapping

NEXUS_MAPPING = [
    {
        "appname": "winpharm",
        "repo": "winpharm-frontend-builds",
        "username": "winpharm-consumer-frontend-builds",
        "matches": ["winpharm.preprod", "winpharm.recette", "winpharm.winproduits", "winpharm.prod"]
    },

    {
        "appname": "pharmalien",
        "repo": "pharmahub-frontend-builds",
        "username": "pharmahub-consumer-frontend-builds",
        "matches": ["pharmalien.dev", "pharmalien.preprod", "pharmalien.prod"]
    },

    {
        "appname": "achatgroupe",
        "repo": "pharmahub-frontend-builds",
        "username": "pharmahub-consumer-frontend-builds",
        "matches": ["lacentrale.dev", "lacentrale.recette", "lacentrale.prod"]
    },
    {
        "appname": "wingroupe",
        "repo": "pharmahub-frontend-builds",
        "username": "pharmahub-consumer-frontend-builds",
        "matches": ["wingroupe.recette", "wingroupe.prod"]
    },
    {
        "appname": "wingroupe",
        "repo": "pharmahub-frontend-builds",
        "username": "pharmahub-consumer-frontend-builds",
        "matches": ["wingroupe.recette", "wingroupe.prod"]
    },
    {
        "appname": "officialpharma",
        "repo": "officialpharma-frontend-builds",
        "username": "officialpharma-consumer-frontend-builds",
        "matches": ["offialpharma.prod"]
    },
    {
        "appname": "webfixgroupe",
        "repo": "webfixgroupe-frontend-builds",
        "username": "webfixgroupe-consumer-frontend-builds",
        "matches": ["webfix.prod", "webfix.dev"]
    },
    {
        "appname": "winchat",
        "repo": "winchat-frontend-builds",
        "username": "winchat-consumer-frontend-builds",
        "matches": ["winchat.prod"]
    },
    {
        "appname": "windoc",
        "repo": "windoc-frontend-builds",
        "username": "windoc-consumer-frontend-builds",
        "matches": ["windoc.prod"]
    },
    {
        "appname": "winref",
        "repo": "winref-frontend-builds",
        "username": "winref-consumer-frontend-builds",
        "matches": ["winref.prod","winref.dev"]
    }


]
