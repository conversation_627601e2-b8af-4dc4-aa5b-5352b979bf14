---
# Single app sync tasks - called from main playbook
- name: Display target app information
  debug:
    msg:
      - "Target App: {{ target_app }}"
      - "Target VPS: {{ target_vps }}"
      - "Script Path: {{ vps_script_paths[target_app] }}"

- name: Execute sync script on target host
  command: "{{ vps_script_paths[target_app] }}"
  register: sync_result
  delegate_to: "{{ target_vps }}"
  vars:
    ansible_ssh_pass: "{{ target_password }}"
    ansible_ssh_user: "ubuntu"

- name: Display sync result
  debug:
    msg:
      - "App: {{ target_app }}"
      - "Host: {{ target_vps }}"
      - "Script: {{ vps_script_paths[target_app] }}"
      - "Result: {{ 'SUCCESS' if sync_result.rc == 0 else 'FAILED' }}"
      - "Output: {{ sync_result.stdout }}"
      - "Error: {{ sync_result.stderr }}"
      - "Return Code: {{ sync_result.rc }}"

- name: Fail if sync was unsuccessful
  fail:
    msg: "Sync failed for {{ target_app }} on {{ target_vps }}"
  when: sync_result.rc != 0