---
# Single app mode tasks
- name: Debug single app mode variables
  debug:
    msg: "Single app mode - target_app: {{ target_app }}, target_vps: {{ target_vps }}"

- name: Verify if the target app is valid (single app mode)
  fail:
    msg: "The target app '{{ target_app }}' is not valid. Allowed apps: {{ app_to_vps.keys() | list }}"
  when: target_app not in app_to_vps

- name: Execute the Product Sync script on the target VPS (single app mode)
  command: >
    "{{ vps_script_paths[target_app] }}"
  delegate_to: "{{ target_vps }}"
  vars:
    ansible_ssh_pass: "{{ target_password }}"
  register: maintenance_result
  when: target_app in vps_script_paths
  notify:
    - Report status

- name: Set run status and message (single app mode)
  set_fact:
    run_status: "{{ 'success' if maintenance_result.rc == 0 else 'failure' }}"
    run_message: >-
      {{ 'Product & Fournisseur and Transco Produit sync script succeeded for ' + target_app if maintenance_result.rc == 0
        else 'Product & Fournisseur and Transco Produit sync script failed for ' + target_app + ': ' + maintenance_result.stderr }}
  when: maintenance_result is defined and not maintenance_result.skipped | default(false)

- name: Display script output (single app mode)
  debug:
    var: maintenance_result.stdout_lines
  when: maintenance_result is defined and not maintenance_result.skipped | default(false)

- name: Trigger handlers immediately (single app mode)
  meta: flush_handlers
  when: maintenance_result is defined and not maintenance_result.skipped | default(false)
  
- name: Check for errors (single app mode)
  fail:
    msg: "Sync script failed with error: {{ maintenance_result.stderr }}"
  when: maintenance_result is defined and not maintenance_result.skipped | default(false) and maintenance_result.rc != 0
