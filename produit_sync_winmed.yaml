- name: Execute PostgreSQL command in Docker container
  hosts: vps6
  become: yes
  remote_user: ubuntu
  vars_files:
    - vars/apps_to_vps.yaml
    - vars/sync_produit.yaml
  vars:
     ansible_ssh_pass: "{{ lookup('env', 'VPS6_SSH_PASSWORD') }}"
     ansible_port: "{{ lookup('env', 'SSH_PORT') }}"
     vps5_ip:  "{{ lookup('env', 'VPS5_IP') }}"
     ssh_password: "{{ lookup('env', 'VPS5_SSH_TRANSFER_PASSWORD') }}"
     vps5_user: "{{ lookup('env', 'VPS5_TRANSFER_USER') }}"
     target_password: "{{ lookup('env', target_vps | upper + '_SSH_PASSWORD') }}"
     run_status : ''



  tasks:
    - name: Run SQL script in Docker container
      command: >
        docker container exec pgsql.preprod
        psql -U postgres -d winproduit_master -f "/db_dump/winproduit/export_winproduit_master_for_pharmahub.sql"

    - name: Push prd.csv file to VPS5
      ansible.builtin.command:
            sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/prd.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
    - name: Copy prd.csv file from VPS5 to VPS4
      vars:
        ansible_ssh_pass: "{{ lookup('env', 'VPS4_SSH_PASSWORD') }}"
      ansible.builtin.command:
        sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
        {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/prd.csv /home/<USER>/docker_dir/shared_local_volumes/prod/winmed/moorsmed-back/
      delegate_to: vps4

    - name: Execute syncDrugs.js script in winmed.prod container
      command: >
        docker container exec winmed.prod
        node /var/prod/winmed/moorsmed-back/scripts/syncDrugs.js
      delegate_to: vps4
      vars:
        ansible_ssh_pass: "{{ lookup('env', 'VPS4_SSH_PASSWORD') }}"
      register: sync_result
      
    - name: Set run status and message
      set_fact:
        run_status: "{{ 'success' if sync_result.rc == 0 else 'failure' }}"
        run_message: >-
          {{ '✅ Product sync script succeeded for Winmed' if sync_result.rc == 0
          else '❌ Product sync script failed for Winmed' + sync_result.stderr }}
      notify:
        - Report status

    - name: Display script output
      debug:
        var: sync_result.stdout_lines

    - name: Report status
      uri:
        url: "{{ lookup('env', 'SLACK_WEBHOOK_URL') }}"
        method: POST
        headers:
          Content-Type: "application/json"
        body: |
          {
          "text": "{{ run_message }}"
          }
        body_format: json
