echo "Hello World!"

#  print vars

echo "the env var is $SEMAPHORE_GIT_PR_NUMBER"

echo "the env var is $SEMAPHORE_GIT_REPO_SLUG"

echo "the env var is $SEMAPHORE_GIT_SHA"

echo "the env var is $SEMAPHORE_GIT_BRANCH"

echo "the env var is $SEMAPHORE_GIT_MESSAGE"

echo "the env var is $SEMAPHORE_GIT_COMMIT_MESSAGE"

echo "the env var is $SEMAPHORE_GIT_COMMIT_AUTHOR"

echo "the env var is $SEMAPHORE_GIT_COMMIT_TIMESTAMP"

echo "the env var is $SEMAPHORE_WORKFLOW_ID"

echo "the env var is $SEMAPHORE_WORKFLOW_NAME"

echo "the env var is $SEMAPHORE_WORKFLOW_URL"

echo "the env var is $SEMAPHORE_JOB_ID"

echo "the env var is $SEMAPHORE_JOB_NAME"

echo "the env var is $SEMAPHORE_JOB_URL"

echo "the env var is $SEMAPHORE_BUILD_NUMBER"

echo "the env var is $SEMAPHORE_PROJECT_NAME"

echo "the env var is $say_hello"

echo "the env var is $target"