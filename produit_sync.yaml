- name: Execute PostgreSQL command in Docker container
  hosts: vps6
  become: yes
  remote_user: ubuntu
  vars_files:
    - vars/apps_to_vps.yaml
    - vars/sync_produit.yaml
  vars:
     ansible_ssh_pass: "{{ lookup('env', 'VPS6_SSH_PASSWORD') }}"
     ansible_port: "{{ lookup('env', 'SSH_PORT') }}"
     vps5_ip:  "{{ lookup('env', 'VPS5_IP') }}"
     ssh_password: "{{ lookup('env', 'VPS5_SSH_TRANSFER_PASSWORD') }}"
     vps5_user: "{{ lookup('env', 'VPS5_TRANSFER_USER') }}"
     # Specific prod apps to process when target_app is 'all.prod'
     selected_prod_apps:
       - webfixgroupe.prod
       - pharmalien.prod
       - wingroupe.prod
       - lacentrale.prod
     # Variables for single app mode
     target_vps: "{{ app_to_vps[target_app] if target_app is defined and target_app != 'all.prod' else '' }}"
     target_password: "{{ lookup('env', target_vps | upper + '_SSH_PASSWORD') if target_app is defined and target_app != 'all.prod' else '' }}"
     run_status : ''



  tasks:

    - name : Debug  vars target_app and vps and selected apps
      debug:
        msg: "target_app: {{ target_app }}, target_vps: {{ target_vps }}, selected_prod_apps: {{ selected_prod_apps }}" 
   
         
    - name: Run SQL script in Docker container
      command: >
        docker container exec pgsql.preprod
        psql -U postgres -d winproduit_master -f "/db_dump/winproduit/export_winproduit_master_for_pharmahub.sql"
    - name: Push prd.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/prd.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/

    - name: Push frn.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/frn.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
    
    - name: Push transco.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/transco.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
  
    # Single app mode (original functionality)
    - name: Verify if the target app is valid (single app mode)
      fail:
        msg: "The target app '{{ target_app }}' is not valid. Allowed apps: {{ app_to_vps.keys() | list }}"
      when: target_app is defined and target_app != 'all.prod' and target_app not in app_to_vps

    - name: Debug single app mode condition
      debug:
        msg: "Single app mode check - target_app: {{ target_app }}, is not all.prod: {{ target_app != 'all.prod' }}, target_vps not empty: {{ target_vps != '' }}"
      when: target_app is defined

    - name: Execute the Product Sync script on the target VPS (single app mode)
      command: >
        "{{ vps_script_paths[target_app] }}"
      delegate_to: "{{ target_vps }}"
      vars:
        ansible_ssh_pass: "{{ target_password }}"
      register: maintenance_result
      when:
        - target_app is defined
        - target_app != 'all.prod'
        - target_vps != ''
        - target_app in vps_script_paths
      notify:
        - Report status

    - name: Set run status and message (single app mode)
      set_fact:
        run_status: "{{ 'success' if maintenance_result.rc == 0 else 'failure' }}"
        run_message: >-
          {{ 'Product & Fournisseur and Transco Produit sync script succeeded for ' + target_app if maintenance_result.rc == 0
            else 'Product & Fournisseur and Transco Produit sync script failed for ' + target_app + ': ' + maintenance_result.stderr }}
      when:
        - target_app is defined
        - target_app != 'all.prod'
        - target_vps != ''
        - maintenance_result is defined
        - not maintenance_result.skipped | default(false)

    - name: Display script output (single app mode)
      debug:
        var: maintenance_result.stdout_lines
      when:
        - target_app is defined
        - target_app != 'all.prod'
        - target_vps != ''
        - maintenance_result is defined
        - not maintenance_result.skipped | default(false)

    - name: Trigger handlers immediately (single app mode)
      meta: flush_handlers
      when:
        - target_app is defined
        - target_app != 'all.prod'
        - target_vps != ''
        - maintenance_result is defined
        - not maintenance_result.skipped | default(false)

    - name: Check for errors (single app mode)
      fail:
        msg: "Sync script failed with error: {{ maintenance_result.stderr }}"
      when:
        - target_app is defined
        - target_app != 'all.prod'
        - target_vps != ''
        - maintenance_result is defined
        - not maintenance_result.skipped | default(false)
        - maintenance_result.rc != 0

    # Multiple prod apps mode
    - name: Display selected prod apps that will be processed
      debug:
        msg: "Processing selected prod apps: {{ selected_prod_apps }}"
      when: target_app is defined and target_app == 'all.prod'

    - name: Execute the Product Sync script for each selected prod app
      command: >
        "{{ vps_script_paths[item] }}"
      delegate_to: "{{ app_to_vps[item] }}"
      vars:
        ansible_ssh_pass: "{{ lookup('env', app_to_vps[item] | upper + '_SSH_PASSWORD') }}"
      register: maintenance_results
      loop: "{{ selected_prod_apps }}"
      loop_control:
        loop_var: item
      when: target_app is defined and target_app == 'all.prod' and item in vps_script_paths

    - name: Display results for each prod app
      debug:
        msg:
          - "App: {{ item.item }}"
          - "Status: {{ 'Success' if item.rc == 0 else 'Failed' }}"
          - "Output: {{ item.stdout_lines | default(['No output']) }}"
      loop: "{{ maintenance_results.results | default([]) }}"
      loop_control:
        loop_var: item
      when: target_app is defined and target_app == 'all.prod' and maintenance_results is defined

    - name: Report status for each prod app to Slack
      uri:
        url: "{{ lookup('env', 'SLACK_WEBHOOK_URL') }}"
        method: POST
        headers:
          Content-Type: "application/json"
        body: |
          {
            "text": "{{ 'Product & Fournisseur and Transco Produit sync script ' + ('succeeded' if item.rc == 0 else 'failed') + ' for ' + item.item + ('' if item.rc == 0 else ': ' + item.stderr) }}"
          }
        body_format: json
      loop: "{{ maintenance_results.results | default([]) }}"
      loop_control:
        loop_var: item
      when: target_app is defined and target_app == 'all.prod' and maintenance_results is defined and lookup('env', 'SLACK_WEBHOOK_URL') != ''

    - name: Check for any failures in prod apps
      fail:
        msg: "One or more sync scripts failed. Check the output above for details."
      when: target_app is defined and target_app == 'all.prod' and maintenance_results is defined and maintenance_results.results | selectattr('rc', 'ne', 0) | list | length > 0



  handlers:
    - name: Report status
      uri:
        url: "{{ lookup('env', 'SLACK_WEBHOOK_URL') }}"
        method: POST
        headers:
          Content-Type: "application/json"
        body: |
          {
            "text": "{{ run_message }}"
          }
        body_format: json
