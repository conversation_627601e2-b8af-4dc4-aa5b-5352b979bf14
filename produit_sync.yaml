---
- name: Execute PostgreSQL command and sync operations
  hosts: vps6
  become: yes
  remote_user: ubuntu
  gather_facts: false
  vars_files:
    - vars/apps_to_vps.yaml
    - vars/sync_produit.yaml
  vars:
     ansible_ssh_pass: "{{ lookup('env', 'VPS6_SSH_PASSWORD') }}"
     ansible_port: "{{ lookup('env', 'SSH_PORT') }}"
     vps5_ip: "{{ lookup('env', 'VPS5_IP') }}"
     ssh_password: "{{ lookup('env', 'VPS5_SSH_TRANSFER_PASSWORD') }}"
     vps5_user: "{{ lookup('env', 'VPS5_TRANSFER_USER') }}"
     
     # Specific prod apps to process when target_app is 'all.prod'
     selected_prod_apps:
      #  - webfixgroupe.prod
      #  - pharmalien.prod
      #  - wingroupe.prod
       - lacentrale.prod
     
     run_status: ''
     
     # Determine operation mode
     operation_mode: "{{ 'single' if target_app is defined and target_app != 'all.prod' else 'multi' if target_app == 'all.prod' else 'unknown' }}"

  tasks:
    - name: Debug vars target_app and selected apps
      debug:
        msg: "target_app: {{ target_app | default('not defined') }}, mode: {{ operation_mode }}, selected_prod_apps: {{ selected_prod_apps }}"

    - name: Run SQL script in Docker container
      command: >
        docker container exec pgsql.preprod
        psql -U postgres -d winproduit_master -f "/db_dump/winproduit/export_winproduit_master_for_pharmahub.sql"
      register: sql_result
      
    - name: Push prd.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/prd.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
      register: prd_transfer

    - name: Push frn.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/frn.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
      register: frn_transfer
    
    - name: Push transco.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/transco.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
      register: transco_transfer

    # Single app mode
    - name: Set single app mode variables
      set_fact:
        target_vps: "{{ app_to_vps[target_app] }}"
        target_password: "{{ lookup('env', app_to_vps[target_app] | upper + '_SSH_PASSWORD') }}"
        run_message: "Starting single app sync for {{ target_app }} on {{ app_to_vps[target_app] }}"
      when: operation_mode == 'single'

    - name: Include single app sync tasks
      include_tasks: tasks/single_app_sync.yaml
      when: operation_mode == 'single'

    # Multi app mode
    - name: Set multi app mode variables
      set_fact:
        run_message: "Starting multi app sync for {{ selected_prod_apps | length }} apps: {{ selected_prod_apps | join(', ') }}"
      when: operation_mode == 'multi'

    - name: Include multi app sync tasks
      include_tasks: tasks/multi_app_sync.yaml
      when: operation_mode == 'multi'

    # Error handling for unknown mode
    - name: Handle unknown operation mode
      fail:
        msg: "Unknown operation mode. Please set target_app to a valid app name or 'all.prod'"
      when: operation_mode == 'unknown'

    # Success notification
    - name: Set success message
      set_fact:
        run_message: "✅ Sync completed successfully for {{ target_app if operation_mode == 'single' else selected_prod_apps | join(', ') }}"
      when: operation_mode in ['single', 'multi']

  handlers:
    - name: Report status
      uri:
        url: "{{ lookup('env', 'SLACK_WEBHOOK_URL') }}"
        method: POST
        headers:
          Content-Type: "application/json"
        body: |
          {
            "text": "{{ run_message }}"
          }
        body_format: json
      listen: "notify slack"

  post_tasks:
    - name: Send final notification
      debug:
        msg: "{{ run_message }}"
      notify: notify slack