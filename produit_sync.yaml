- name: Execute PostgreSQL command in Docker container
  hosts: vps6
  become: yes
  remote_user: ubuntu
  vars_files:
    - vars/apps_to_vps.yaml
    - vars/sync_produit.yaml
  vars:
     ansible_ssh_pass: "{{ lookup('env', 'VPS6_SSH_PASSWORD') }}"
     ansible_port: "{{ lookup('env', 'SSH_PORT') }}"
     vps5_ip:  "{{ lookup('env', 'VPS5_IP') }}"
     ssh_password: "{{ lookup('env', 'VPS5_SSH_TRANSFER_PASSWORD') }}"
     vps5_user: "{{ lookup('env', 'VPS5_TRANSFER_USER') }}"
     # Filter apps to get only those containing 'prod'
     prod_apps: "{{ app_to_vps.keys() | select('search', 'prod') | list }}"
     run_status : ''



  tasks:
    - name: Run SQL script in Docker container
      command: >
        docker container exec pgsql.preprod
        psql -U postgres -d winproduit_master -f "/db_dump/winproduit/export_winproduit_master_for_pharmahub.sql"
    - name: Push prd.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/prd.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/

    - name: Push frn.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/frn.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
    
    - name: Push transco.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/transco.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
  
    - name: Display prod apps that will be processed
      debug:
        msg: "Processing prod apps: {{ prod_apps }}"

    - name: Execute the Product Sync script on each prod app
      block:
        - name: Execute the Product Sync script
          command: >
            "{{ vps_script_paths[item] }}"
          delegate_to: "{{ app_to_vps[item] }}"
          vars:
            ansible_ssh_pass: "{{ lookup('env', app_to_vps[item] | upper + '_SSH_PASSWORD') }}"
          register: maintenance_result
          when: item in vps_script_paths

        - name: Set run status and message for current app
          set_fact:
            run_status: "{{ 'success' if maintenance_result.rc == 0 else 'failure' }}"
            run_message: >-
              {{ 'Product & Fournisseur and Transco Produit sync script succeeded for ' + item if maintenance_result.rc == 0
                else 'Product & Fournisseur and Transco Produit sync script failed for ' + item + ': ' + maintenance_result.stderr }}
          when: item in vps_script_paths and maintenance_result is defined

        - name: Display script output for current app
          debug:
            msg:
              - "App: {{ item }}"
              - "Output: {{ maintenance_result.stdout_lines | default('No output') }}"
          when: item in vps_script_paths and maintenance_result is defined

        - name: Report status for current app
          uri:
            url: "{{ lookup('env', 'SLACK_WEBHOOK_URL') }}"
            method: POST
            headers:
              Content-Type: "application/json"
            body: |
              {
                "text": "{{ run_message }}"
              }
            body_format: json
          when: item in vps_script_paths and maintenance_result is defined and lookup('env', 'SLACK_WEBHOOK_URL') != ''

        - name: Check for errors for current app
          debug:
            msg: "Sync script failed for {{ item }} with error: {{ maintenance_result.stderr }}"
          when: item in vps_script_paths and maintenance_result is defined and maintenance_result.rc != 0

        - name: Skip app if no script path defined
          debug:
            msg: "Skipping {{ item }} - no script path defined in vps_script_paths"
          when: item not in vps_script_paths

      loop: "{{ prod_apps }}"
      loop_control:
        loop_var: item



  handlers: []
