- name: Execute PostgreSQL command in Docker container
  hosts: vps6
  become: yes
  remote_user: ubuntu
  vars_files:
    - vars/apps_to_vps.yaml
    - vars/sync_produit.yaml
  vars:
     ansible_ssh_pass: "{{ lookup('env', 'VPS6_SSH_PASSWORD') }}"
     ansible_port: "{{ lookup('env', 'SSH_PORT') }}"
     vps5_ip:  "{{ lookup('env', 'VPS5_IP') }}"
     ssh_password: "{{ lookup('env', 'VPS5_SSH_TRANSFER_PASSWORD') }}"
     vps5_user: "{{ lookup('env', 'VPS5_TRANSFER_USER') }}"
     target_vps: "{{ app_to_vps[target_app] }}"
     target_password: "{{ lookup('env', target_vps | upper + '_SSH_PASSWORD') }}"
     run_status : ''



  tasks:
    - name: Run SQL script in Docker container
      command: >
        docker container exec pgsql.preprod
        psql -U postgres -d winproduit_master -f "/db_dump/winproduit/export_winproduit_master_for_pharmahub.sql"
    - name: Push prd.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/prd.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/

    - name: Push frn.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/frn.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
    
    - name: Push transco.csv file to VPS5
      ansible.builtin.command:
        cmd: >
          sshpass -p "{{ ssh_password }}" rsync -avz -e "ssh -p {{ ansible_port }} -o StrictHostKeyChecking=no"
          /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/winproduit/export/transco.csv
          {{ vps5_user }}@{{ vps5_ip }}:/home/<USER>/
  
    - name: Verify if the target app is valid
      fail:
        msg: "The target app '{{ target_app }}' is not valid. Allowed apps: {{ app_to_vps.keys() | list }}"
      when: target_app not in app_to_vps

    - name: Execute the Product Sync script on the target VPS
      command: >
        "{{ vps_script_paths[target_app] }}"
      delegate_to: "{{ target_vps  }}"
      vars:
        ansible_ssh_pass: "{{ target_password }}"
      register: maintenance_result
      notify:
        - Report status
    

    - name: Set run status and message
      set_fact:
        run_status: "{{ 'success' if maintenance_result.rc == 0 else 'failure' }}"
        run_message: >-
          {{ 'Product & Fournisseur and Transco Produit sync script succeeded for ' + target_app if maintenance_result.rc == 0
            else 'Product & Fournisseur and Transco Produit sync script failed for ' + target_app + ': ' + maintenance_result.stderr }}

    - name: Display script output
      debug:
        var: maintenance_result.stdout_lines

    - name: Trigger handlers immediately
      meta: flush_handlers
      
    - name: Check for errors
      fail:
        msg: "S script failed with error: {{ maintenance_result.stderr }}"
      when: maintenance_result.rc != 0



  handlers:
    - name: Report status
      uri:
        url: "{{ lookup('env', 'SLACK_WEBHOOK_URL') }}"
        method: POST
        headers:
          Content-Type: "application/json"
        body: |
          {
            "text": "{{ run_message }}"
          }
        body_format: json
