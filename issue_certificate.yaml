- name: Backup the database of the target app
  hosts: localhost  # Start on localhost to determine the target VPS
  gather_facts: false
  remote_user: ubuntu
  vars_files:
    - vars/apps_to_vps.yaml
    - vars/domain_to_vps.yaml

  vars:
      target_password: "{{ lookup('env', target_vps | upper + '_SSH_PASSWORD') }}"
      target_vps: "{{ domain_to_vps[target_domain] }}"

  tasks:
    - name: Verify if the target app is valid
      fail:
        msg: "The target Domain '{{ target_domain }}' is not valid. Allowed apps: {{ domain_to_vps.keys() | list }}"
      when: target_domain not in domain_to_vps

    - name: Set the script path for the target VPS
      set_fact:
        script_path: "{{ path_to_certbot_script[target_vps] }}"

        
    - name: Execute Certbot script on the target VPS
      command: >
        "{{ script_path }}" "{{ target_domain }}"
      delegate_to: "{{ target_vps  }}"
      vars:
        ansible_ssh_pass: "{{ target_password }}"
      register: backup_result

    - name: Display script output
      debug:
        var: backup_result.stdout_lines
        

