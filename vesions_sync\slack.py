import json
import requests
import os
from datetime import datetime
from state import state
def read_slack_webhook():
    """Read the SLACK_WEBHOOK URL from state or environment variable"""
    try:
        # Try to get from environment variable first
        webhook_url = state.get("SLACK_WEBHOOK") or os.environ.get("SLACK_WEBHOOK")
        if webhook_url:
            return webhook_url
    except Exception as e:
        print(f"Error reading SLACK_WEBHOOK: {e}")
    return None

def send_slack_message(app_name, environment, status, message, details=None):
    """
    Send a message to Slack with app name and environment information
    
    Args:
        app_name (str): Name of the application
        environment (str): Environment (prod, dev, etc.)
        status (str): "success" or "error"
        message (str): Main message to send
        details (str, optional): Additional details or error information
    
    Returns:
        bool: True if message was sent successfully, False otherwise
    """
    webhook_url = read_slack_webhook()
    if not webhook_url:
        print("No Slack webhook URL found. Message not sent.")
        return False
    
    # Set color based on status
    color = "#36a64f" if status.lower() == "success" else "#ff0000"

    title = f"Version Sync - {app_name.upper()}.{environment.upper()} Success" if status.lower() == "success" else f"Version Sync - {app_name.upper()}.{environment.upper()} Failed"
    
    # Format timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Create the payload
    payload = {
        "text": title,
        "attachments": [
            {
                "color": color,
                "blocks": [
                    {
                        "type": "header",
                        "text": {
                            "type": "plain_text",
                            # "text": f"[{status.upper()}] {app_name}"
                            "text": f"{app_name.upper()} - VERSION SYNC",
                        }
                    },
                    {
                        "type": "section",
                        "fields": [
                            {
                                "type": "mrkdwn",
                                "text": f"*Environment:*\n{environment}"
                            },
                            {
                                "type": "mrkdwn",
                                "text": f"*Time:*\n{timestamp}"
                            }
                        ]
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*Message:*\n{message}"
                        }
                    }
                ]
            }
        ]
    }
    
    # Add details if provided
    if details:
        payload["attachments"][0]["blocks"].append({
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Details:*\n```{details}```"
            }
        })
    
    try:
        response = requests.post(webhook_url, json=payload)
        if response.status_code == 200:
            return True
        else:
            print(f"Failed to send Slack message. Status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error sending Slack message: {e}")
        return False

def send_success_message(app_name, environment, message, details=None):
    """Convenience function to send a success message"""
    return send_slack_message(app_name, environment, "success", message, details)

def send_error_message(app_name, environment, message, details=None):
    """Convenience function to send an error message"""
    return send_slack_message(app_name, environment, "error", message, details)
