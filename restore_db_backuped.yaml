- name: Restore DB
  hosts: localhost    
  gather_facts: false
  remote_user: ubuntu
  vars_files:
    - vars/apps_to_db_name.yaml
    - vars/apps_to_vps.yaml
    - vars/db_compatibility.yaml
  vars:
      target_app : "winoffre.dev"
      source_app: "winoffre.prod"
      backup_before: "no"
      target_password: "{{ lookup('env', target_vps | upper + '_SSH_PASSWORD') }}"
      target_vps: "{{ app_to_vps[target_app] }}"
      source_password: "{{ lookup('env', source_vps | upper + '_SSH_PASSWORD') }}"
      source_vps: "{{ app_to_vps[source_app] }}"
      source_db: "{{ app_to_db_name[source_app] }}"
      target_db: "{{ app_to_db_name[target_app] }}"
      source_env: "{{ source_app.split('.')[-1] }}"
      target_env: "{{ target_app.split('.')[-1] }}"
      pgsql_source: "pgsql.{{ source_env }}"
      pgsql_target: "pgsql.{{ target_env }}"


  tasks:
    - name: Verify if the target app is valid
      fail:
        msg: "The target app '{{ target_app }}' is not valid. Allowed apps: {{ app_to_vps.keys() | list }}"
      when: target_app not in app_to_vps

    - name: Verify target app does not end with .prod
      fail:
        msg: "You can't restore to a prod database"
      when: target_app is regex('.*\.prod$') and target_app != "tap.prod"

    
    - name: Check if source and target databases are compatible
      fail:
        msg: "Incompatible databases: '{{ source_db }}' cannot be restored to '{{ target_db }}'"
      when: target_db not in db_compatibility.get(source_db, [])
    
    
    - name : change pgsql source if source wingroupe.prod
      set_fact:
        pgsql_source: "pgsql.preprod"
        source_env: "preprod"
      when: source_app == "winref.prod" or source_app == "winproduit.prod"


    - name: Debug Start Process
      debug:
        msg: "{{ debuginfo | to_json }}"
      vars:
        debuginfo:
          process: "Restore DB from {{ source_app }} to {{ target_app }}"
          database:
            source: "{{ app_to_db_name[source_app] }}"
            target: "{{ app_to_db_name[target_app] }}"
          vps:
            source: "{{ source_vps }}"
            target: "{{ target_vps }}"

    - name: Execute Docker command inside source container
      shell: |
        stdbuf -oL  docker exec {{ pgsql_source }} mkdir -p /db_dump/ansible_backups
        stdbuf -oL  docker exec {{ pgsql_source }} pg_dump -U postgres -d {{ source_db }} -F c -f /db_dump/ansible_backups/{{ source_db }}.dump
      delegate_to: "{{ source_vps }}"
      become: true  # Ensure privilege escalation if needed
      register: docker_output
      changed_when: false
      vars:
        ansible_ssh_pass: "{{ source_password }}"

    - name: Debug Docker Output
      debug:
        var: docker_output.stdout

    - name: Copy the dump file from source to local 
      fetch:
        src: "/home/<USER>/docker_dir/shared_local_volumes/{{source_env}}/postgres.{{source_env}}/ansible_backups/{{ source_db }}.dump"
        dest: "/tmp/{{ source_db }}.dump"
        flat: yes
      delegate_to: "{{ source_vps }}"
      register: fetch_output
      changed_when: false
      vars:
        ansible_ssh_pass: "{{ source_password }}"

    - name: Create the target directory
      shell: |
        mkdir -p /home/<USER>/docker_dir/shared_local_volumes/{{target_env}}/postgres.{{target_env}}/ansible_backups
      delegate_to: "{{ target_vps }}"
      register: create_output
      vars:
        ansible_ssh_pass: "{{ target_password }}"

    - name: Copy File To Target vps
      copy:
        src: "/tmp/{{ source_db }}.dump"
        dest: "/home/<USER>/docker_dir/shared_local_volumes/{{target_env}}/postgres.{{target_env}}/ansible_backups/{{ source_db }}.dump"
      delegate_to: "{{ target_vps }}"
      register: copy_output
      changed_when: false
      vars:
        ansible_ssh_pass: "{{ target_password }}"

  # Optional Case if you want to backup the target database before restoring
    - name: Get current timestamp for backup filename
      set_fact:
        backup_timestamp: "{{ lookup('pipe', 'date +%Y%m%d_%H%M%S') }}"
      when: backup_before == "yes"

    - name: Backup target database before dropping
      shell: |
        docker exec {{ pgsql_target }} mkdir -p /db_dump/ansible_backups
        docker exec {{ pgsql_target }} pg_dump -U postgres -d {{ target_db }} -F c -f /db_dump/ansible_backups/{{ target_db }}_backuped_before_restore_{{ backup_timestamp }}.dump
      delegate_to: "{{ target_vps }}"
      become: true
      vars:
        ansible_ssh_pass: "{{ target_password }}"
      when: backup_before == "yes"
      register: backup_output

    - name: Display backup information
      debug:
        msg: "Created backup of {{ target_db }} database as {{ target_db }}_backuped_before_restore_{{ backup_timestamp }}.dump"
      when: backup_before == "yes" and backup_output is defined

    - name: Drop the existing database if it exists
      shell: |
        docker exec {{ pgsql_target }} psql -U postgres -c "DROP DATABASE  {{ target_db }} WITH (FORCE);"
        docker exec {{ pgsql_target }} psql -U postgres -c "CREATE DATABASE  {{ target_db }};"

      delegate_to: "{{ target_vps }}"
      become: true
    
    #  now i need to Run docker command in pgsql.{{target_env}} to restore the database drop create restore
    - name: Execute Docker command For Restore Database inside pgsql.{{target_env}}
      shell: |
        docker exec {{ pgsql_target }} pg_restore -U postgres -d {{ target_db }}  -F c /db_dump/ansible_backups/{{ source_db }}.dump
      delegate_to: "{{ target_vps }}"
      become: true
      vars:
        ansible_ssh_pass: "{{ target_password }}"

    - name: Report status
      uri:
        url: "{{ lookup('env', 'SLACK_WEBHOOK_URL') }}"
        method: POST
        headers:
          Content-Type: "application/json"
        body: |
          {
            "text": "Database Restore Completed for {{ target_app }} from {{ source_app }}",
            "attachments": [
              {
                "title": "Database Restore Report",
                "fields": [
                  {
                    "title": "Source Database",
                    "value": "{{ source_app }} ({{ source_db }})",
                    "short": true
                  },
                  {
                    "title": "Target Database",
                    "value": "{{ target_app }} ({{ target_db }})",
                    "short": true
                  },
                  {
                    "title": "Status",
                    "value": "Success",
                    "short": true
                  },
                  {
                    "title": "Backuped Before Restore",
                    "value": "{{ 'Yes' if backup_before == 'yes' else 'No' }}",
                    "short": true
                  }
                ]
              }
            ]
          }
        body_format: json
