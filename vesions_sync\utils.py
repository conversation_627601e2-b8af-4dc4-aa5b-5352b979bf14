

from const import APPS_TO_JOB_PATH, APPS_TO_VPS, JENKINS_URLS, NEXUS_MAPPING

def resolve_job_path(app_name):
    """
    Resolves the job path for a given application name.
    
    Args:
        app_name (str): The name of the application.
    
    Returns:
        str: The resolved job path.
    """
    vps = APPS_TO_VPS.get(app_name)
    if vps:
        return f"{JENKINS_URLS[vps]}/{APPS_TO_JOB_PATH[app_name]}"
    else:
        print(f"Application {app_name} not found in mapping.")
        return None

def resolve_target_vps(appname):
    """
    Resolves the VPS for a given application name.
    
    Args:
        app_name (str): The name of the application.
    
    Returns:
        str: The resolved VPS.
    """
    vps = APPS_TO_VPS.get(appname)
    if vps:
        return vps
    else:
        print(f"Application {appname} not found in mapping.")
        exit(1)
        return None
    

def get_nexus_config_by_match(match_string):
    for config in NEXUS_MAPPING:
        if "matches" in config and match_string in config["matches"]:
            # Create a copy of the config without the matches key
            result = config.copy()
            result.pop("matches", None)
            return result
    return None
