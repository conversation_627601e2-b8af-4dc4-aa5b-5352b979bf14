<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site en maintenance</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet">
    <style>
        *,
        ::before,
        ::after {
            --tw-border-spacing-x: 0;
            --tw-border-spacing-y: 0;
            --tw-translate-x: 0;
            --tw-translate-y: 0;
            --tw-rotate: 0;
            --tw-skew-x: 0;
            --tw-skew-y: 0;
            --tw-scale-x: 1;
            --tw-scale-y: 1;
            --tw-pan-x: ;
            --tw-pan-y: ;
            --tw-pinch-zoom: ;
            --tw-scroll-snap-strictness: proximity;
            --tw-gradient-from-position: ;
            --tw-gradient-via-position: ;
            --tw-gradient-to-position: ;
            --tw-ordinal: ;
            --tw-slashed-zero: ;
            --tw-numeric-figure: ;
            --tw-numeric-spacing: ;
            --tw-numeric-fraction: ;
            --tw-ring-inset: ;
            --tw-ring-offset-width: 0px;
            --tw-ring-offset-color: #fff;
            --tw-ring-color: rgb(59 130 246 / 0.5);
            --tw-ring-offset-shadow: 0 0 #0000;
            --tw-ring-shadow: 0 0 #0000;
            --tw-shadow: 0 0 #0000;
            --tw-shadow-colored: 0 0 #0000;
            --tw-blur: ;
            --tw-brightness: ;
            --tw-contrast: ;
            --tw-grayscale: ;
            --tw-hue-rotate: ;
            --tw-invert: ;
            --tw-saturate: ;
            --tw-sepia: ;
            --tw-drop-shadow: ;
            --tw-backdrop-blur: ;
            --tw-backdrop-brightness: ;
            --tw-backdrop-contrast: ;
            --tw-backdrop-grayscale: ;
            --tw-backdrop-hue-rotate: ;
            --tw-backdrop-invert: ;
            --tw-backdrop-opacity: ;
            --tw-backdrop-saturate: ;
            --tw-backdrop-sepia: ;
            --tw-contain-size: ;
            --tw-contain-layout: ;
            --tw-contain-paint: ;
            --tw-contain-style: ;
        }

        ::backdrop {
            --tw-border-spacing-x: 0;
            --tw-border-spacing-y: 0;
            --tw-translate-x: 0;
            --tw-translate-y: 0;
            --tw-rotate: 0;
            --tw-skew-x: 0;
            --tw-skew-y: 0;
            --tw-scale-x: 1;
            --tw-scale-y: 1;
            --tw-pan-x: ;
            --tw-pan-y: ;
            --tw-pinch-zoom: ;
            --tw-scroll-snap-strictness: proximity;
            --tw-gradient-from-position: ;
            --tw-gradient-via-position: ;
            --tw-gradient-to-position: ;
            --tw-ordinal: ;
            --tw-slashed-zero: ;
            --tw-numeric-figure: ;
            --tw-numeric-spacing: ;
            --tw-numeric-fraction: ;
            --tw-ring-inset: ;
            --tw-ring-offset-width: 0px;
            --tw-ring-offset-color: #fff;
            --tw-ring-color: rgb(59 130 246 / 0.5);
            --tw-ring-offset-shadow: 0 0 #0000;
            --tw-ring-shadow: 0 0 #0000;
            --tw-shadow: 0 0 #0000;
            --tw-shadow-colored: 0 0 #0000;
            --tw-blur: ;
            --tw-brightness: ;
            --tw-contrast: ;
            --tw-grayscale: ;
            --tw-hue-rotate: ;
            --tw-invert: ;
            --tw-saturate: ;
            --tw-sepia: ;
            --tw-drop-shadow: ;
            --tw-backdrop-blur: ;
            --tw-backdrop-brightness: ;
            --tw-backdrop-contrast: ;
            --tw-backdrop-grayscale: ;
            --tw-backdrop-hue-rotate: ;
            --tw-backdrop-invert: ;
            --tw-backdrop-opacity: ;
            --tw-backdrop-saturate: ;
            --tw-backdrop-sepia: ;
            --tw-contain-size: ;
            --tw-contain-layout: ;
            --tw-contain-paint: ;
            --tw-contain-style: ;
        }

        /*
! tailwindcss v3.4.15 | MIT License | https://tailwindcss.com
*/
        /*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
        *,
        ::before,
        ::after {
            box-sizing: border-box;
            /* 1 */
            border-width: 0;
            /* 2 */
            border-style: solid;
            /* 2 */
            border-color: #e5e7eb;
            /* 2 */
        }

        ::before,
        ::after {
            --tw-content: '';
        }

        /*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
        html,
        :host {
            line-height: 1.5;
            /* 1 */
            -webkit-text-size-adjust: 100%;
            /* 2 */
            -moz-tab-size: 4;
            /* 3 */
            tab-size: 4;
            /* 3 */
            font-family: Inter;
            /* 4 */
            font-feature-settings: normal;
            /* 5 */
            font-variation-settings: normal;
            /* 6 */
            -webkit-tap-highlight-color: transparent;
            /* 7 */
        }

        /*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
        body {
            margin: 0;
            /* 1 */
            line-height: inherit;
            /* 2 */
        }

        /*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
        hr {
            height: 0;
            /* 1 */
            color: inherit;
            /* 2 */
            border-top-width: 1px;
            /* 3 */
        }

        /*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
        abbr:where([title]) {
            text-decoration: underline dotted;
        }

        /*
Remove the default font size and weight for headings.
*/
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-size: inherit;
            font-weight: inherit;
        }

        /*
Reset links to optimize for opt-in styling instead of opt-out.
*/
        a {
            color: inherit;
            text-decoration: inherit;
        }

        /*
Add the correct font weight in Edge and Safari.
*/
        b,
        strong {
            font-weight: bolder;
        }

        /*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
        code,
        kbd,
        samp,
        pre {
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            /* 1 */
            font-feature-settings: normal;
            /* 2 */
            font-variation-settings: normal;
            /* 3 */
            font-size: 1em;
            /* 4 */
        }

        /*
Add the correct font size in all browsers.
*/
        small {
            font-size: 80%;
        }

        /*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
        sub,
        sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline;
        }

        sub {
            bottom: -0.25em;
        }

        sup {
            top: -0.5em;
        }

        /*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
        table {
            text-indent: 0;
            /* 1 */
            border-color: inherit;
            /* 2 */
            border-collapse: collapse;
            /* 3 */
        }

        /*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
        button,
        input,
        optgroup,
        select,
        textarea {
            font-family: inherit;
            /* 1 */
            font-feature-settings: inherit;
            /* 1 */
            font-variation-settings: inherit;
            /* 1 */
            font-size: 100%;
            /* 1 */
            font-weight: inherit;
            /* 1 */
            line-height: inherit;
            /* 1 */
            letter-spacing: inherit;
            /* 1 */
            color: inherit;
            /* 1 */
            margin: 0;
            /* 2 */
            padding: 0;
            /* 3 */
        }

        /*
Remove the inheritance of text transform in Edge and Firefox.
*/
        button,
        select {
            text-transform: none;
        }

        /*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
        button,
        input:where([type='button']),
        input:where([type='reset']),
        input:where([type='submit']) {
            -webkit-appearance: button;
            /* 1 */
            background-color: transparent;
            /* 2 */
            background-image: none;
            /* 2 */
        }

        /*
Use the modern Firefox focus style for all focusable elements.
*/
        :-moz-focusring {
            outline: auto;
        }

        /*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
        :-moz-ui-invalid {
            box-shadow: none;
        }

        /*
Add the correct vertical alignment in Chrome and Firefox.
*/
        progress {
            vertical-align: baseline;
        }

        /*
Correct the cursor style of increment and decrement buttons in Safari.
*/
        ::-webkit-inner-spin-button,
        ::-webkit-outer-spin-button {
            height: auto;
        }

        /*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
        [type='search'] {
            -webkit-appearance: textfield;
            /* 1 */
            outline-offset: -2px;
            /* 2 */
        }

        /*
Remove the inner padding in Chrome and Safari on macOS.
*/
        ::-webkit-search-decoration {
            -webkit-appearance: none;
        }

        /*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
        ::-webkit-file-upload-button {
            -webkit-appearance: button;
            /* 1 */
            font: inherit;
            /* 2 */
        }

        /*
Add the correct display in Chrome and Safari.
*/
        summary {
            display: list-item;
        }

        /*
Removes the default spacing and border for appropriate elements.
*/
        blockquote,
        dl,
        dd,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        hr,
        figure,
        p,
        pre {
            margin: 0;
        }

        fieldset {
            margin: 0;
            padding: 0;
        }

        legend {
            padding: 0;
        }

        ol,
        ul,
        menu {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        /*
Reset default styling for dialogs.
*/
        dialog {
            padding: 0;
        }

        /*
Prevent resizing textareas horizontally by default.
*/
        textarea {
            resize: vertical;
        }

        /*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
        input::placeholder,
        textarea::placeholder {
            opacity: 1;
            /* 1 */
            color: #9ca3af;
            /* 2 */
        }

        /*
Set the default cursor for buttons.
*/
        button,
        [role="button"] {
            cursor: pointer;
        }

        /*
Make sure disabled buttons don't get the pointer cursor.
*/
        :disabled {
            cursor: default;
        }

        /*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
        img,
        svg,
        video,
        canvas,
        audio,
        iframe,
        embed,
        object {
            display: block;
            /* 1 */
            vertical-align: middle;
            /* 2 */
        }

        /*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
        img,
        video {
            max-width: 100%;
            height: auto;
        }

        /* Make elements with the HTML hidden attribute stay hidden by default */
        [hidden]:where(:not([hidden="until-found"])) {
            display: none;
        }

        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mb-8 {
            margin-bottom: 2rem;
        }

        .mt-5 {
            margin-top: 1.25rem;
        }

        .flex {
            display: flex;
        }

        .inline-flex {
            display: inline-flex;
        }

        .size-2 {
            width: 0.5rem;
            height: 0.5rem;
        }

        .h-24 {
            height: 6rem;
        }

        .h-4 {
            height: 1rem;
        }

        .min-h-screen {
            min-height: 100vh;
        }

        .w-24 {
            width: 6rem;
        }

        .w-4 {
            width: 1rem;
        }

        .w-full {
            width: 100%;
        }

        .max-w-2xl {
            max-width: 42rem;
        }

        @keyframes ping {

            75%,
            100% {
                transform: scale(2);
                opacity: 0;
            }
        }

        .animate-ping {
            animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
        }

        .items-center {
            align-items: center;
        }

        .justify-center {
            justify-content: center;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .rounded-2xl {
            border-radius: 1rem;
        }

        .rounded-full {
            border-radius: 9999px;
        }

        .rounded-md {
            border-radius: 0.375rem;
        }

        .bg-indigo-100 {
            --tw-bg-opacity: 1;
            background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
        }

        .bg-indigo-600 {
            --tw-bg-opacity: 1;
            background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
        }

        .bg-red-600 {
            --tw-bg-opacity: 1;
            background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
        }

        .bg-white {
            --tw-bg-opacity: 1;
            background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
        }

        .bg-gradient-to-br {
            background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
        }

        .from-blue-100 {
            --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
            --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
            --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
        }

        .to-indigo-100 {
            --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);
        }

        .p-4 {
            padding: 1rem;
        }

        .p-8 {
            padding: 2rem;
        }

        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .py-3 {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }

        .text-center {
            text-align: center;
        }

        .text-3xl {
            font-size: 1.875rem;
            line-height: 2.25rem;
        }

        .text-sm {
            font-size: 0.875rem;
            line-height: 1.25rem;
        }

        .text-xl {
            font-size: 1.25rem;
            line-height: 1.75rem;
        }

        .font-bold {
            font-weight: 700;
        }

        .font-medium {
            font-weight: 500;
        }

        .text-gray-600 {
            --tw-text-opacity: 1;
            color: rgb(75 85 99 / var(--tw-text-opacity, 1));
        }

        .text-gray-900 {
            --tw-text-opacity: 1;
            color: rgb(17 24 39 / var(--tw-text-opacity, 1));
        }

        .text-white {
            --tw-text-opacity: 1;
            color: rgb(255 255 255 / var(--tw-text-opacity, 1));
        }

        .shadow-sm {
            --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        }

        .shadow-xl {
            --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        }

        .transition-colors {
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
        }

        .duration-200 {
            transition-duration: 200ms;
        }

        .hover\:bg-indigo-700:hover {
            --tw-bg-opacity: 1;
            background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
        }

        .focus\:outline-none:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
        }

        .focus\:ring-2:focus {
            --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
            --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
            box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
        }

        .focus\:ring-indigo-500:focus {
            --tw-ring-opacity: 1;
            --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
        }

        .focus\:ring-offset-2:focus {
            --tw-ring-offset-width: 2px;
        }
    </style>
</head>

<body>
    <div class="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 p-4">
        <div class="w-full max-w-2xl rounded-2xl bg-white p-8 text-center shadow-xl">
            <div class="mb-8">
                <div class="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-indigo-100">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                        xml:space="preserve" width="6.82666in" height="6.82666in"
                        style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
                        viewBox="0 0 6.82666 6.82666">
                        <defs>
                            <style type="text/css">
                                <![CDATA[
                                .fil2 {
                                    fill: none
                                }

                                .fil1 {
                                    fill: #7B1FA2
                                }

                                .fil0 {
                                    fill: #AB47BC
                                }

                                .fil5 {
                                    fill: #B71C1C;
                                    fill-rule: nonzero
                                }

                                .fil3 {
                                    fill: #E53935;
                                    fill-rule: nonzero
                                }

                                .fil4 {
                                    fill: #FDD835;
                                    fill-rule: nonzero
                                }
                                ]]>
                            </style>
                            <clipPath id="id0">
                                <path
                                    d="M3.41333 0c1.88513,0 3.41333,1.5282 3.41333,3.41333 0,1.88513 -1.5282,3.41333 -3.41333,3.41333 -1.88513,0 -3.41333,-1.5282 -3.41333,-3.41333 0,-1.88513 1.5282,-3.41333 3.41333,-3.41333z" />
                            </clipPath>
                        </defs>
                        <g id="Layer_x0020_1">
                            <metadata id="CorelCorpID_0Corel-Layer" />
                            <path class="fil0"
                                d="M3.41333 0c1.88513,0 3.41333,1.5282 3.41333,3.41333 0,1.88513 -1.5282,3.41333 -3.41333,3.41333 -1.88513,0 -3.41333,-1.5282 -3.41333,-3.41333 0,-1.88513 1.5282,-3.41333 3.41333,-3.41333z" />
                            <g style="clip-path:url(#id0)">
                                <g id="_414977720">
                                    <g>
                                        <polygon id="_4343090641" class="fil1"
                                            points="3.78299,1.71156 7.56926,5.49783 7.57754,5.50713 7.58563,5.51665 7.5935,5.52635 7.60118,5.53626 7.60865,5.54636 7.61591,5.55664 7.62296,5.56711 7.62979,5.57775 7.63641,5.58856 7.6428,5.59955 7.64898,5.61069 7.65493,5.622 7.66066,5.63345 7.66616,5.64506 7.67143,5.65681 7.67646,5.66871 7.68127,5.68074 7.68583,5.6929 7.69016,5.70518 7.69425,5.71759 7.69809,5.73012 7.70169,5.74276 7.70504,5.7555 7.70813,5.76835 7.71098,5.78131 7.71357,5.79435 7.7159,5.80748 7.71797,5.8207 7.71979,5.83401 7.72133,5.84739 3.93507,2.06111 3.93352,2.04774 3.9317,2.03443 3.92963,2.02121 3.9273,2.00808 3.92471,1.99504 3.92186,1.98208 3.91877,1.96923 3.91542,1.95648 3.91182,1.94385 3.90798,1.93132 3.90389,1.91891 3.89957,1.90663 3.895,1.89446 3.8902,1.88244 3.88516,1.87054 3.87989,1.85879 3.87439,1.84718 3.86866,1.83572 3.86271,1.82442 3.85653,1.81328 3.85014,1.80229 3.84352,1.79148 3.83669,1.78083 3.82964,1.77037 3.82238,1.76009 3.81491,1.74999 3.80724,1.74008 3.79936,1.73037 3.79127,1.72086 " />
                                        <polygon id="_434309568" class="fil1"
                                            points="3.93507,2.06111 7.72133,5.84739 7.7385,6.01008 3.95224,2.22381 " />
                                        <polygon id="_434309832" class="fil1"
                                            points="3.95224,2.22381 7.7385,6.01008 7.75492,6.16564 3.96865,2.37937 " />
                                        <polygon id="_434310552" class="fil1"
                                            points="3.96865,2.37937 7.75492,6.16564 7.80516,6.64171 4.01889,2.85544 " />
                                        <polygon id="_434310504" class="fil1"
                                            points="4.01889,2.85544 7.80516,6.64171 7.82157,6.79727 4.03531,3.011 " />
                                        <polygon id="_434310600" class="fil1"
                                            points="4.03531,3.011 7.82157,6.79727 7.87181,7.27335 4.08554,3.48707 " />
                                        <polygon id="_434310480" class="fil1"
                                            points="4.08554,3.48707 7.87181,7.27335 7.88823,7.42891 4.10196,3.64263 " />
                                        <polygon id="_434310216" class="fil1"
                                            points="4.10196,3.64263 7.88823,7.42891 7.93847,7.90498 4.1522,4.11871 " />
                                        <polygon id="_434309976" class="fil1"
                                            points="4.1522,4.11871 7.93847,7.90498 7.95488,8.06054 4.16861,4.27427 " />
                                        <polygon id="_434310048" class="fil1"
                                            points="4.16861,4.27427 7.95488,8.06054 7.99514,8.44206 4.20887,4.65579 " />
                                        <polygon id="_434310360" class="fil1"
                                            points="4.68682,4.67857 8.47309,8.46485 8.47811,8.47037 8.48258,8.47636 8.48648,8.48277 8.48975,8.48957 8.49237,8.49672 8.49429,8.50417 8.49546,8.51189 8.49587,8.51984 4.7096,4.73357 4.7092,4.72562 4.70802,4.7179 4.7061,4.71044 4.70348,4.7033 4.70021,4.6965 4.69631,4.69009 4.69184,4.6841 " />
                                        <polygon id="_434310096" class="fil1"
                                            points="4.7096,4.73357 8.49587,8.51984 8.49587,8.98849 4.7096,5.20222 " />
                                        <polygon id="_434310432" class="fil1"
                                            points="4.7096,5.20222 8.49587,8.98849 8.49546,8.99644 8.49429,9.00417 8.49237,9.01162 8.48975,9.01877 8.48648,9.02556 8.48258,9.03198 8.47811,9.03796 8.47309,9.04349 8.46756,9.04851 8.46157,9.05299 8.45516,9.05688 8.44836,9.06016 8.44122,9.06278 8.43376,9.06469 8.42604,9.06587 8.41809,9.06627 4.63182,5.28 4.63977,5.2796 4.6475,5.27842 4.65495,5.2765 4.66209,5.27389 4.66889,5.27061 4.67531,5.26672 4.6813,5.26224 4.68682,5.25722 4.69184,5.25169 4.69631,5.2457 4.70021,5.23929 4.70348,5.2325 4.7061,5.22535 4.70802,5.21789 4.7092,5.21017 " />
                                        <polygon id="_434311080" class="fil1"
                                            points="4.63182,5.28 8.41809,9.06627 5.98111,9.06627 2.19484,5.28 " />
                                        <polygon id="_434310720" class="fil1"
                                            points="2.19484,5.28 5.98111,9.06627 5.97315,9.06587 5.96543,9.06469 5.95798,9.06278 5.95083,9.06016 5.94404,9.05688 5.93762,9.05299 5.93163,9.04851 2.14537,5.26224 2.15135,5.26672 2.15777,5.27061 2.16456,5.27389 2.17171,5.2765 2.17917,5.27842 2.18689,5.2796 " />
                                    </g>
                                    <path id="_414978320" class="fil1"
                                        d="M3.41333 1.54667c0.140728,0 0.261776,0.0561811 0.3525,0.146906 0.0936142,0.0936142 0.154142,0.224457 0.16924,0.367543l0.0171693 0.162693 0.0164134 0.155559 0.0502402 0.476075 0.0164173 0.155559 0.0502362 0.476075 0.0164173 0.155559 0.0502402 0.476075 0.0164134 0.155559 0.0402598 0.381524 0.422945 0c0.0429567,0 0.0777795,0.0348268 0.0777795,0.0777795l0 0.46865c0,0.0429528 -0.0348228,0.0777795 -0.0777795,0.0777795l-2.43698 0c-0.0429528,0 -0.0777795,-0.0348268 -0.0777795,-0.0777795l0 -0.46865c0,-0.0429528 0.0348268,-0.0777795 0.0777795,-0.0777795l0.422937 0 0.0402598 -0.381524 0.0164173 -0.155559 0.0502362 -0.476075 0.0164173 -0.155559 0.0502402 -0.476075 0.0164134 -0.155559 0.0502402 -0.476075 0.0164173 -0.155559 0.0171693 -0.162693c0.0150984,-0.143106 0.075626,-0.273945 0.169232,-0.367551 0.0907283,-0.0907283 0.211776,-0.146898 0.352508,-0.146898z" />
                                </g>
                            </g>
                            <path class="fil2"
                                d="M3.41333 0c1.88513,0 3.41333,1.5282 3.41333,3.41333 0,1.88513 -1.5282,3.41333 -3.41333,3.41333 -1.88513,0 -3.41333,-1.5282 -3.41333,-3.41333 0,-1.88513 1.5282,-3.41333 3.41333,-3.41333z" />
                            <path class="fil3"
                                d="M3.41333 1.54667c0.140728,0 0.261776,0.0561811 0.3525,0.146906 0.0936142,0.0936142 0.154142,0.224457 0.16924,0.367543l0.281157 2.66432 0.000204724 -2.3622e-005c0.00450787,0.0427165 -0.0264685,0.0810039 -0.069185,0.0855118 -0.0028937,0.00030315 -0.00576378,0.000444882 -0.0086063,0.000429134l-1.45107 0c-0.0429528,0 -0.0777795,-0.0348228 -0.0777795,-0.0777795 0,-0.00475197 0.000429134,-0.00940551 0.00124409,-0.0139252l0.280551 -2.65853c0.0150984,-0.143106 0.075626,-0.273945 0.169232,-0.367551 0.0907283,-0.0907283 0.211776,-0.146898 0.352508,-0.146898z" />
                            <path class="fil4"
                                d="M3.95224 2.22381l0.0164134 0.155559 -1.11065 0 0.0164173 -0.155559 1.07782 0zm0.0666535 0.631634l0.0164173 0.155559 -1.24396 0 0.0164134 -0.155559 1.21113 0zm0.0666535 0.631634l0.0164173 0.155559 -1.37727 0 0.0164173 -0.155559 1.34444 0zm0.0666575 0.631634l0.0164134 0.155559 -1.51058 0 0.0164173 -0.155559 1.47775 0z" />
                            <path class="fil5"
                                d="M4.63182 5.28l-2.43698 0c-0.0429528,0 -0.0777795,-0.0348268 -0.0777795,-0.0777795l0 -0.46865c0,-0.0429528 0.0348268,-0.0777795 0.0777795,-0.0777795l2.43698 0c0.0429567,0 0.0777795,0.0348268 0.0777795,0.0777795l0 0.46865c0,0.0429528 -0.0348228,0.0777795 -0.0777795,0.0777795z" />
                        </g>
                    </svg>
                </div>
            </div>

            <h1 class="mb-4 text-3xl font-bold text-gray-900">Site en maintenance</h1>

            <p class="mb-8 text-xl text-gray-600">Nous effectuons actuellement des mises à jour pour améliorer votre
                expérience. Nous serons de retour très bientôt !</p>

            <div class="flex items-center justify-center gap-2 text-gray-600">
                <span>Nous travaillons dur pour revenir en ligne</span>
                <div class="size-2 animate-ping rounded-full bg-red-600"></div>
            </div>
            <div class="mt-5">
                <button
                    onclick="window.location.reload()"
                    class="inline-flex items-center justify-center gap-2 rounded-md bg-indigo-600 px-4 py-3 text-sm font-medium text-white shadow-sm transition-colors duration-200 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-refresh-cw h-4 w-4">
                        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                        <path d="M21 3v5h-5"></path>
                        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                        <path d="M8 16H3v5"></path>
                    </svg>
                    Actualiser la page
                </button>
            </div>
        </div>
    </div>

</body>

</html>