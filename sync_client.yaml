---
- name: Sync <PERSON>leint to target app ({{ target_app }})
  hosts: localhost
  gather_facts: no
  vars_files:
    - vars/domain_to_app.yaml

  vars:
    api_path: "/api/conso_ext/batch/phmaroc"
    bearer_token: "{{ lookup('env','PLATFORM_SYNC_CLIENT_TOKEN')}}"

  tasks:
    - name: Check if target_app exists in mapping
      fail:
        msg: "Target app '{{ target_app }}' is not defined in domain_to_app."
      when: target_app not in domain_to_app

    - name: Make HTTP request to the target app
      uri:
        url: "{{ domain_to_app[target_app] }}{{ api_path }}"
        method: GET
        headers:
          Authorization: "Bearer {{ bearer_token }}"
        return_content: yes
      register: response

    - name: Debug response
      debug:
        var: response.json
