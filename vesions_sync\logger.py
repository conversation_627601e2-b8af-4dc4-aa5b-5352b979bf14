import logging
import os
from datetime import datetime
from state import state
class Logger:
    def __init__(self, log_file=None, log_level=logging.INFO):
        """
        Initialize a logger that outputs to console and file.
        
        Args:
            log_file (str): Path to the log file. If None, a default filename will be used.
            log_level (int): Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        self.log_file = log_file
        self.log_level = log_level
        self.current_date = datetime.now().strftime('%Y-%m-%d')
        self._setup_logger()

    def _setup_logger(self):
        """
        Set up the logger with the appropriate handlers and formatters.
        """
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(self.log_level)
        self.logger.handlers = []  # Clear any existing handlers

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # Create file handler
        if self.log_file is None:
            logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
            os.makedirs(logs_dir, exist_ok=True)
            self.log_file = os.path.join(logs_dir, f'app_{self.current_date}.log')

        file_handler = logging.FileHandler(self.log_file)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

    def _update_log_file(self):
        """
        Update the log file if the date has changed.
        """
        new_date = datetime.now().strftime('%Y-%m-%d')
        if new_date != self.current_date:
            self.current_date = new_date
            if self.log_file is None:
                logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
                self.log_file = os.path.join(logs_dir, f'app_{self.current_date}.log')
            self._setup_logger()

    def debug(self, message):
        if state.get("DEBUG"):
            self._update_log_file()
            self.logger.debug(message)

    def info(self, message):
        self._update_log_file()
        self.logger.info(message)

    def warning(self, message):
        self._update_log_file()
        self.logger.warning(message)

    def error(self, message):
        self._update_log_file()
        self.logger.error(message)

    def critical(self, message):
        self._update_log_file()
        self.logger.critical(message)



log = Logger()