app_to_db_name:
  pharmalien.dev: plateforme_offres
  lacentrale.dev: achat_groupe
  lacentrale.preprod: achat_groupe_recette
  webfixgroupe.dev: webfixgroupe
  winpharm.preprod: winpharm
  winpharm.recette: winpharm_recette
  pharmalien.preprod: plateforme_offres
  winref.dev: winproduit_master_dev

  # prod
  lacentrale.prod: achat_groupe
  wingroupe.prod: wingroupe
  pharmalien.prod: plateforme_offres
  winpharm.prod: winpharmprod
  officielpharma.prod: officielpharma
  webfixgroupe.prod: webfixgroupe
  winproduit.prod : winproduit_master
  winref.prod: winproduit_master
  semaphore: semaphore
  zabbix: zabbix
  glitch: glitch
  tap.prod: winproduit_master_bis


backup_script_paths:
  pharmalien.dev: /home/<USER>/docker_dir/scripts/backup_db_dev.sh
  lacentrale.dev: /home/<USER>/docker_dir/scripts/backup_db_dev.sh
  lacentrale.preprod: /home/<USER>/docker_dir/scripts/backup_db_preprod.sh
  webfixgroupe.dev: /home/<USER>/docker_dir/scripts/backup_db_dev.sh
  winpharm.preprod: /home/<USER>/docker_dir/scripts/backup_db_preprod.sh
  winpharm.recette: /home/<USER>/docker_dir/scripts/backup_db_preprod.sh

  # prod
  lacentrale.prod: /home/<USER>/docker_dir/scripts/backup_db.sh
  pharmalien.prod: /home/<USER>/docker_dir/scripts/backup_db.sh
  winpharm.prod: /home/<USER>/docker_dir/scripts/backup_db.sh
  officielpharma.prod: /home/<USER>/docker_dir/scripts/backup_db.sh
  semaphore: /home/<USER>/docker_dir/scripts/backup_db.sh
  wingroupe.prod: /home/<USER>/docker_dir/scripts/backup_db.sh
  zabbix: /home/<USER>/docker_dir/scripts/backup_db.sh
  glitch: /home/<USER>/docker_dir/scripts/backup_db.sh
  webfixgroupe.prod: /home/<USER>/docker_dir/scripts/backup_db.sh
  winproduit.prod: /home/<USER>/docker_dir/scripts/backup_db.sh
  tap.prod: /home/<USER>/docker_dir/scripts/backup_db.sh